<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= esc($page_header) ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('staff/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('staff/farms') ?>">Farm Blocks</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Details</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('staff/farms/' . $farm_block['id'] . '/edit') ?>" class="btn btn-warning me-2">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?= base_url('staff/farms') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <!-- Farm Block Details -->
    <div class="row">
        <!-- Main Details -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-seedling"></i> Farm Block Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Block Code:</td>
                                    <td>
                                        <span class="badge bg-primary fs-6"><?= esc($farm_block['block_code']) ?></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Farmer:</td>
                                    <td>
                                        <?php if ($farmer): ?>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <span class="avatar-title bg-soft-primary text-primary rounded-circle">
                                                        <?= strtoupper(substr($farmer['given_name'], 0, 1)) ?>
                                                    </span>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0"><?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></h6>
                                                    <small class="text-muted">
                                                        <i class="fas fa-<?= $farmer['gender'] == 'Male' ? 'mars' : 'venus' ?>"></i>
                                                        <?= esc($farmer['gender']) ?> | Code: <?= esc($farmer['farmer_code']) ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">Farmer not found</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Crop:</td>
                                    <td>
                                        <?php if ($crop): ?>
                                            <span class="badge fs-6" style="background-color: <?= esc($crop['crop_color_code']) ?>; color: white;">
                                                <?= esc($crop['crop_name']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">Crop not found</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Village:</td>
                                    <td><?= esc($farm_block['village']) ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Block Site:</td>
                                    <td><?= esc($farm_block['block_site']) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">LLG:</td>
                                    <td><?= $llg ? esc($llg['name']) : 'Not specified' ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Ward:</td>
                                    <td><?= $ward ? esc($ward['name']) : 'Not specified' ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">GPS Coordinates:</td>
                                    <td>
                                        <?php if ($farm_block['lat'] && $farm_block['lon']): ?>
                                            <div>
                                                <small class="text-muted">Latitude:</small> <?= esc($farm_block['lat']) ?><br>
                                                <small class="text-muted">Longitude:</small> <?= esc($farm_block['lon']) ?>
                                            </div>
                                            <a href="https://www.google.com/maps?q=<?= esc($farm_block['lat']) ?>,<?= esc($farm_block['lon']) ?>" 
                                               target="_blank" class="btn btn-sm btn-outline-primary mt-1">
                                                <i class="fas fa-map-marker-alt"></i> View on Map
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td>
                                        <span class="badge bg-<?= $farm_block['status'] == 'active' ? 'success' : 'secondary' ?>">
                                            <?= ucfirst($farm_block['status']) ?>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if ($farm_block['remarks']): ?>
                        <div class="mt-3">
                            <h6 class="fw-bold">Remarks:</h6>
                            <div class="alert alert-light">
                                <?= nl2br(esc($farm_block['remarks'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-tools"></i> Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="#" class="btn btn-outline-primary w-100">
                                <i class="fas fa-seedling"></i><br>
                                <small>Crop Data</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="#" class="btn btn-outline-success w-100">
                                <i class="fas fa-cut"></i><br>
                                <small>Harvest Data</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="#" class="btn btn-outline-warning w-100">
                                <i class="fas fa-flask"></i><br>
                                <small>Fertilizer Data</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="#" class="btn btn-outline-danger w-100">
                                <i class="fas fa-bug"></i><br>
                                <small>Pesticide Data</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Record Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Record Information
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm table-borderless">
                        <tr>
                            <td class="fw-bold">Created:</td>
                            <td><?= date('M d, Y \a\t g:i A', strtotime($farm_block['created_at'])) ?></td>
                        </tr>
                        <?php if ($farm_block['updated_at']): ?>
                        <tr>
                            <td class="fw-bold">Last Updated:</td>
                            <td><?= date('M d, Y \a\t g:i A', strtotime($farm_block['updated_at'])) ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <td class="fw-bold">Exercise ID:</td>
                            <td><?= $farm_block['exercise_id'] ? esc($farm_block['exercise_id']) : 'Not assigned' ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Location Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-secondary">
                        <i class="fas fa-map-marker-alt"></i> Location Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="location-hierarchy">
                        <div class="mb-2">
                            <i class="fas fa-globe text-primary"></i>
                            <strong>Country:</strong> Papua New Guinea
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-map text-info"></i>
                            <strong>Province:</strong> <?= session()->get('province_name') ?>
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-city text-warning"></i>
                            <strong>District:</strong> <?= session()->get('district_name') ?>
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-building text-success"></i>
                            <strong>LLG:</strong> <?= $llg ? esc($llg['name']) : 'Not specified' ?>
                        </div>
                        <div class="mb-2">
                            <i class="fas fa-home text-danger"></i>
                            <strong>Ward:</strong> <?= $ward ? esc($ward['name']) : 'Not specified' ?>
                        </div>
                        <div>
                            <i class="fas fa-map-pin text-dark"></i>
                            <strong>Village:</strong> <?= esc($farm_block['village']) ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-cogs"></i> Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('staff/farms/' . $farm_block['id'] . '/edit') ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Farm Block
                        </a>
                        <button type="button" class="btn btn-danger" onclick="confirmDelete(<?= $farm_block['id'] ?>)">
                            <i class="fas fa-trash"></i> Delete Farm Block
                        </button>
                        <a href="<?= base_url('staff/farms') ?>" class="btn btn-secondary">
                            <i class="fas fa-list"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                <p>Are you sure you want to delete the farm block <strong><?= esc($farm_block['block_code']) ?></strong>?</p>
                <p class="text-muted">This will also affect any related crop data, harvest records, and other associated information.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Farm Block
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = '<?= base_url('staff/farms/') ?>' + id;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.bg-soft-primary {
    background-color: rgba(13, 110, 253, 0.1);
}

.location-hierarchy div {
    padding-left: 20px;
    position: relative;
}

.location-hierarchy div:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 8px;
    top: 20px;
    height: 15px;
    border-left: 1px dashed #dee2e6;
}
</style>

<?= $this->endSection() ?>
