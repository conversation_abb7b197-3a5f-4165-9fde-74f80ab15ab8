<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><?= esc($page_header) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/crop-buyers') ?>">Crop Buyers</a></li>
                            <li class="breadcrumb-item active">Add New</li>
                        </ol>
                    </nav>
                </div>
                <a href="<?= base_url('staff/crop-buyers') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong>Validation Errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Create Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Add New Crop Buyer</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('staff/crop-buyers') ?>" method="post" id="cropBuyerForm">
                        <?= csrf_field() ?>

                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">Basic Information</h6>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Buyer Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name') ?>" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            <div class="col-md-6">
                                <label for="crop_id" class="form-label">Crop <span class="text-danger">*</span></label>
                                <select class="form-select" id="crop_id" name="crop_id" required>
                                    <option value="">Select Crop...</option>
                                    <?php foreach ($crops as $crop): ?>
                                        <option value="<?= esc($crop['id']) ?>" 
                                                <?= old('crop_id') == $crop['id'] ? 'selected' : '' ?>>
                                            <?= esc($crop['crop_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">Contact Information</h6>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="contact_number" class="form-label">Contact Number</label>
                                <input type="text" class="form-control" id="contact_number" name="contact_number" 
                                       value="<?= old('contact_number') ?>" placeholder="e.g., +675 123 4567">
                                <div class="invalid-feedback"></div>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= old('email') ?>" placeholder="<EMAIL>">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <!-- Operation Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">Operation Information</h6>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="operation_span" class="form-label">Operation Span <span class="text-danger">*</span></label>
                                <select class="form-select" id="operation_span" name="operation_span" required>
                                    <option value="">Select Operation Span...</option>
                                    <option value="local" <?= old('operation_span') == 'local' ? 'selected' : '' ?>>
                                        Local (Province Wide)
                                    </option>
                                    <option value="national" <?= old('operation_span') == 'national' ? 'selected' : '' ?>>
                                        National (Country Wide)
                                    </option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                            <div class="col-md-6">
                                <label for="location_id" class="form-label">Location</label>
                                <select class="form-select" id="location_id" name="location_id">
                                    <option value="">Select Location...</option>
                                    <?php foreach ($provinces as $province): ?>
                                        <option value="<?= esc($province['id']) ?>" 
                                                <?= old('location_id') == $province['id'] ? 'selected' : '' ?>>
                                            <?= esc($province['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="form-text text-muted">Select province for local operations</small>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control" id="address" name="address" rows="3" 
                                          placeholder="Enter full address..."><?= old('address') ?></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="Additional information about the buyer..."><?= old('description') ?></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>

                        <div class="text-end">
                            <a href="<?= base_url('staff/crop-buyers') ?>" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                Save Crop Buyer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Form submission handling
    $('#cropBuyerForm').on('submit', function() {
        $('#submitBtn').prop('disabled', true);
        $('#submitBtn .spinner-border').removeClass('d-none');
    });

    // Operation span change handler
    $('#operation_span').on('change', function() {
        const locationSelect = $('#location_id');
        const locationLabel = $('label[for="location_id"]');
        
        if ($(this).val() === 'local') {
            locationSelect.prop('disabled', false);
            locationLabel.html('Province <span class="text-danger">*</span>');
            locationSelect.attr('required', true);
        } else if ($(this).val() === 'national') {
            locationSelect.prop('disabled', true);
            locationSelect.val('');
            locationLabel.html('Location');
            locationSelect.removeAttr('required');
        } else {
            locationSelect.prop('disabled', false);
            locationLabel.html('Location');
            locationSelect.removeAttr('required');
        }
    });

    // Trigger change event on page load to set initial state
    $('#operation_span').trigger('change');
});
</script>
<?= $this->endSection() ?>
