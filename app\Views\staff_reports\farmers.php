<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Add a container-fluid class for full-width -->
<div class="container-fluid">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('staff/dashboard') ?>">Dashboard</a></li>
            <li class="breadcrumb-item">Reports</li>
            <li class="breadcrumb-item active" aria-current="page">Farmers Report</li>
        </ol>
    </nav>
    
    <!-- Charts Row -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6 col-lg-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Gender Distribution</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="genderChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Farmers per Ward</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="wardChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-12 col-lg-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Farmers per LLG</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="llgChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Table Card -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>LLG and Ward Summary</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>LLG</th>
                            <th>Ward</th>
                            <th>Total Farmers</th>
                            <th>Male Farmers</th>
                            <th>Female Farmers</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $llg_totals = [];
                        foreach ($farmers as $farmer) {
                            $llg = $farmer['llg_name'] ?? 'Unknown';
                            $ward = $farmer['ward_name'] ?? 'Unknown';
                            $key = $llg . '|' . $ward;
                            
                            if (!isset($llg_totals[$key])) {
                                $llg_totals[$key] = [
                                    'llg' => $llg,
                                    'ward' => $ward,
                                    'total' => 0,
                                    'male' => 0,
                                    'female' => 0
                                ];
                            }
                            
                            $llg_totals[$key]['total']++;
                            if ($farmer['gender'] === 'Male') {
                                $llg_totals[$key]['male']++;
                            } elseif ($farmer['gender'] === 'Female') {
                                $llg_totals[$key]['female']++;
                            }
                        }

                        // Sort by LLG and then Ward
                        ksort($llg_totals);
                        
                        foreach ($llg_totals as $data): ?>
                            <tr>
                                <td><?= esc($data['llg']) ?></td>
                                <td><?= esc($data['ward']) ?></td>
                                <td class="text-center"><?= $data['total'] ?></td>
                                <td class="text-center"><?= $data['male'] ?></td>
                                <td class="text-center"><?= $data['female'] ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Crop Distribution by LLG Card -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-seedling me-2"></i>Crop Distribution by LLG</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>LLG</th>
                            <?php foreach ($crop_distribution['crop_types'] as $crop): ?>
                                <th class="text-center"><?= esc($crop) ?></th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($crop_distribution['distribution'] as $llg => $crops): ?>
                            <tr>
                                <td><?= esc($llg) ?></td>
                                <?php 
                                foreach ($crop_distribution['crop_types'] as $crop): 
                                    $count = $crops[$crop] ?? 0;
                                ?>
                                    <td class="text-center">
                                        <?php if ($count > 0): ?>
                                            <span class="badge bg-info"><?= $count ?></span>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="fw-bold">
                            <td>Total</td>
                            <?php 
                            foreach ($crop_distribution['crop_types'] as $crop): 
                                $cropTotal = array_sum(array_map(function($llg) use ($crop) {
                                    return $llg[$crop] ?? 0;
                                }, $crop_distribution['distribution']));
                            ?>
                                <td class="text-center">
                                    <?php if ($cropTotal > 0): ?>
                                        <span class="badge bg-success"><?= $cropTotal ?></span>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                            <?php endforeach; ?>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <!-- Age Distribution Chart -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Age Distribution by LLG</h5>
        </div>
        <div class="card-body">
            <div class="chart-container" style="position: relative; height: 400px;">
                <canvas id="ageDistributionChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Table Card -->
    <div class="card">
        <div class="card-header bg-white d-flex justify-content-between align-items-center flex-wrap gap-2">
            <h5 class="mb-0"><i class="fas fa-users me-2"></i>Farmers Report</h5>
            
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="farmersTable" class="table table-striped table-bordered text-nowrap w-100">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Farmer Code</th>
                            <th>Name</th>
                            <th>Gender</th>
                            <th>Age</th>
                            <th>Children</th>
                            <th>LLG</th>
                            <th>Ward</th>
                            <th>Village</th>
                            <th>Farm Blocks</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $loop_count = 1;
                        foreach ($farmers as $farmer): 
                            // Calculate age from date_of_birth
                            $age = isset($farmer['date_of_birth']) ? 
                                   date_diff(date_create($farmer['date_of_birth']), date_create('today'))->y : 
                                   'N/A';
                        ?>
                            <tr class="clickable-row" data-href="<?= base_url('staff/reports/farmer_profile/' . $farmer['id']) ?>" style="cursor: pointer;">
                                <td><?= $loop_count++ ?></td>
                                <td><?= esc($farmer['farmer_code']) ?></td>
                                <td>
                                    <?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?>
                                </td>
                                <td><?= esc($farmer['gender']) ?></td>
                                <td><?= $age ?></td>
                                <td class="text-center">
                                    <span class="">
                                        <?= $farmer['children_count'] ?>
                                    </span>
                                </td>
                                <td><?= esc($farmer['llg_name']) ?></td>
                                <td><?= esc($farmer['ward_name']) ?></td>
                                <td><?= esc($farmer['village']) ?></td>
                                <td class="text-center">
                                    <span class="badge bg-info">
                                        <?= $farmer['farm_blocks_count'] ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $farmer['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($farmer['status']) ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<!-- Add Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    $(document).ready(function() {
        // Add this at the top - Convert PHP data to JavaScript
        const farmers = <?= json_encode($farmers) ?>;

        // Add click handler for table rows
        $('.clickable-row').on('click', function(e) {
            // Prevent click if user is selecting text
            if (window.getSelection().toString()) {
                return;
            }
            
            // Prevent click if user clicked on a button or link
            if ($(e.target).is('button, a, input, .badge')) {
                return;
            }
            
            window.location = $(this).data('href');
        });

        // Add hover effect for table rows
        $('.clickable-row').hover(
            function() {
                $(this).addClass('table-hover-highlight');
            },
            function() {
                $(this).removeClass('table-hover-highlight');
            }
        );

        // Modified DataTable initialization for better responsiveness
        $('#farmersTable').DataTable({
            dom: '<"row"<"col-sm-12 col-md-6"B><"col-sm-12 col-md-6"f>>rtip',
            buttons: [{
                extend: 'collection',
                text: 'Export',
                buttons: ['copy', 'csv', 'excel', 'pdf', 'print']
            }],
            pageLength: 25,
            order: [
                [1, 'asc']
            ],
            scrollX: true,
            responsive: false,
            columnDefs: [{
                className: 'text-center',
                targets: [3, 5, 9]
            }]
        });

        // Add custom CSS for hover effect
        $('<style>')
            .text(`
                .table-hover-highlight {
                    background-color: rgba(0, 123, 255, 0.05) !important;
                    transition: background-color 0.2s ease;
                }
                .clickable-row:hover {
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
            `)
            .appendTo('head');

        // Chart configurations with responsive options
        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        padding: 15
                    }
                },
                tooltip: {
                    enabled: true,
                    callbacks: {
                        label: function(context) {
                            const value = context.raw;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${value} (${percentage}%)`;
                        }
                    }
                }
            }
        };

        // Gender Chart - with improved data handling
        const genderData = farmers.reduce((acc, farmer) => {
            const gender = farmer.gender || 'Not Specified';
            acc[gender] = (acc[gender] || 0) + 1;
            return acc;
        }, {});

        // Function to get color based on gender
        const getGenderColor = (gender) => {
            switch (gender) {
                case 'Male':
                    return '#36A2EB'; // Blue
                case 'Female':
                    return '#FF6384'; // Pinkish red
                default:
                    return '#FFCE56'; // Yellow for Not Specified
            }
        };

        new Chart(document.getElementById('genderChart'), {
            type: 'pie',
            data: {
                labels: Object.keys(genderData),
                datasets: [{
                    data: Object.values(genderData),
                    backgroundColor: Object.keys(genderData).map(getGenderColor),
                    borderWidth: 1
                }]
            },
            options: {
                ...chartOptions,
                plugins: {
                    ...chartOptions.plugins,
                    title: {
                        display: true,
                        text: 'Gender Distribution'
                    }
                }
            }
        });

        // Ward Chart - with improved data handling
        const wardData = farmers.reduce((acc, farmer) => {
            const ward = farmer.ward_name || 'Unknown';
            acc[ward] = (acc[ward] || 0) + 1;
            return acc;
        }, {});

        // Sort wards by number of farmers (descending)
        const sortedWards = Object.entries(wardData)
            .sort(([, a], [, b]) => b - a)
            .reduce((acc, [key, value]) => {
                acc[key] = value;
                return acc;
            }, {});

        new Chart(document.getElementById('wardChart'), {
            type: 'bar',
            data: {
                labels: Object.keys(sortedWards),
                datasets: [{
                    label: 'Number of Farmers',
                    data: Object.values(sortedWards),
                    backgroundColor: '#FF9F40',
                    borderWidth: 1
                }]
            },
            options: {
                ...chartOptions,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Farmers per Ward'
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                            autoSkip: false,
                            font: {
                                size: 10
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1,
                            precision: 0
                        }
                    }
                }
            }
        });

        // LLG Chart - with improved data handling
        const llgData = farmers.reduce((acc, farmer) => {
            const llg = farmer.llg_name || 'Unknown';
            acc[llg] = (acc[llg] || 0) + 1;
            return acc;
        }, {});

        // Sort LLGs by number of farmers (descending)
        const sortedLLGs = Object.entries(llgData)
            .sort(([, a], [, b]) => b - a)
            .reduce((acc, [key, value]) => {
                acc[key] = value;
                return acc;
            }, {});

        new Chart(document.getElementById('llgChart'), {
            type: 'bar',
            data: {
                labels: Object.keys(sortedLLGs),
                datasets: [{
                    label: 'Number of Farmers',
                    data: Object.values(sortedLLGs),
                    backgroundColor: '#9966FF',
                    borderWidth: 1
                }]
            },
            options: {
                ...chartOptions,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Farmers per LLG'
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                            autoSkip: false,
                            font: {
                                size: 10
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1,
                            precision: 0
                        }
                    }
                }
            }
        });

        // Age Distribution Chart
        const ageRanges = {
            '18-25': [18, 25],
            '26-35': [26, 35],
            '36-45': [36, 45],
            '46-55': [46, 55],
            '56-64': [56, 64],
            '65-75': [65, 75],
            '76-85': [76, 85],
            '86+': [86, 150]
        };

        const llgAgeData = {};
        farmers.forEach(farmer => {
            const llg = farmer.llg_name || 'Unknown';
            const age = farmer.date_of_birth ? 
                       Math.floor((new Date() - new Date(farmer.date_of_birth)) / 31557600000) : 
                       null;
            
            if (!llgAgeData[llg]) {
                llgAgeData[llg] = Object.keys(ageRanges).reduce((acc, range) => {
                    acc[range] = 0;
                    return acc;
                }, {});
            }

            if (age !== null) {
                for (const [range, [min, max]] of Object.entries(ageRanges)) {
                    if (age >= min && age <= max) {
                        llgAgeData[llg][range]++;
                        break;
                    }
                }
            }
        });

        new Chart(document.getElementById('ageDistributionChart'), {
            type: 'bar',
            data: {
                labels: Object.keys(llgAgeData),
                datasets: Object.keys(ageRanges).map((range, index) => ({
                    label: range,
                    data: Object.values(llgAgeData).map(data => data[range]),
                    backgroundColor: [
                        '#FF6384', // Pink - 18-25
                        '#36A2EB', // Blue - 26-35
                        '#FFCE56', // Yellow - 36-45
                        '#4BC0C0', // Teal - 46-55
                        '#9966FF', // Purple - 56-64
                        '#FF9F40', // Orange - 65-75
                        '#7BC8A4', // Green - 76-85
                        '#EA80FC'  // Violet - 86+
                    ][index],
                    stack: 'Stack 0'
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        stacked: true,
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1,
                            precision: 0
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Age Distribution by LLG'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                }
            }
        });

        // Handle window resize to redraw charts
        window.addEventListener('resize', function() {
            Chart.instances.forEach(chart => chart.resize());
        });
    });

    function exportToExcel() {
        $('.buttons-excel').click();
    }
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
