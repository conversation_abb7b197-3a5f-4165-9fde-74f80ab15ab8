<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb and Back Button -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('staff/office/documents') ?>" class="text-success">Document Folders</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create Folder</li>
        </ol>
    </nav>
    <a href="<?= base_url('staff/office/documents') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Folders
    </a>
</div>

<!-- Main Content -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-folder-plus me-2"></i>Create New Document Folder</h5>
    </div>
    <div class="card-body">
        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger">
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <form action="<?= base_url('staff/office/documents/store') ?>" method="post">
            <?= csrf_field() ?>
            
            <div class="mb-3">
                <label for="folder_name" class="form-label">Folder Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="folder_name" name="folder_name" required value="<?= old('folder_name') ?>">
            </div>
            
            <div class="mb-3">
                <label for="parent_folder_id" class="form-label">Parent Folder</label>
                <select class="form-select" id="parent_folder_id" name="parent_folder_id">
                    <option value="">None (Root Folder)</option>
                    <?php foreach ($folders as $folder) : ?>
                        <option value="<?= $folder['id'] ?>" <?= old('parent_folder_id') == $folder['id'] ? 'selected' : '' ?>>
                            <?= esc($folder['folder_name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <div class="form-text">Optional. Select a parent folder if this is a subfolder.</div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3"><?= old('description') ?></textarea>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="<?= base_url('staff/office/documents') ?>" class="btn btn-outline-secondary me-md-2">Cancel</a>
                <button type="submit" class="btn btn-success">Create Folder</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>
