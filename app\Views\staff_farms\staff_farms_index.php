<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= esc($page_header) ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('staff/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Farm Blocks</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('staff/farms/create') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New Farm Block
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Farm Blocks Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-seedling"></i> Crops Farm Blocks
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($farm_blocks)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="farmBlocksTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Block Code</th>
                                <th>Farmer</th>
                                <th>Crop</th>
                                <th>Location</th>
                                <th>Village</th>
                                <th>Block Site</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($farm_blocks as $block): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-primary"><?= esc($block['block_code']) ?></span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <span class="avatar-title bg-soft-primary text-primary rounded-circle">
                                                    <?= strtoupper(substr($block['farmer_name'], 0, 1)) ?>
                                                </span>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?= esc($block['farmer_name']) ?></h6>
                                                <small class="text-muted">
                                                    <i class="fas fa-<?= $block['gender'] == 'Male' ? 'mars' : 'venus' ?>"></i>
                                                    <?= esc($block['gender']) ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge" style="background-color: <?= esc($block['crop_color_code']) ?>; color: white;">
                                            <?= esc($block['crop_name']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= esc($block['district_name']) ?><br>
                                            <?= esc($block['llg_name']) ?><br>
                                            <?= esc($block['ward_name']) ?>
                                        </small>
                                    </td>
                                    <td><?= esc($block['village']) ?></td>
                                    <td><?= esc($block['block_site']) ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('staff/farms/' . $block['id']) ?>" 
                                               class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('staff/farms/' . $block['id'] . '/edit') ?>" 
                                               class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    onclick="confirmDelete(<?= $block['id'] ?>)" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-seedling fa-3x text-muted"></i>
                    </div>
                    <h5 class="text-muted">No Farm Blocks Found</h5>
                    <p class="text-muted">Start by creating your first farm block.</p>
                    <a href="<?= base_url('staff/farms/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Farm Block
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this farm block? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = '<?= base_url('staff/farms/') ?>' + id;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Initialize DataTable
$(document).ready(function() {
    $('#farmBlocksTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[0, 'asc']],
        columnDefs: [
            { orderable: false, targets: [6] }
        ]
    });
});
</script>

<?= $this->endSection() ?>
