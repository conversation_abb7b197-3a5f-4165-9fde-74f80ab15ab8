<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb and Back Button -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('staff/office/documents') ?>" class="text-success">Document Folders</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('staff/office/documents/files/' . $folder['id']) ?>" class="text-success"><?= esc($folder['folder_name']) ?></a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit File</li>
        </ol>
    </nav>
    <a href="<?= base_url('staff/office/documents/files/' . $folder['id']) ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Files
    </a>
</div>

<!-- Main Content -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Edit File</h5>
    </div>
    <div class="card-body">
        <?php if (session()->getFlashdata('error')) : ?>
            <div class="alert alert-danger">
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <form action="<?= base_url('staff/office/documents/files/update/' . $file['id']) ?>" method="post" enctype="multipart/form-data">
            <?= csrf_field() ?>

            <div class="mb-3">
                <label for="file_name" class="form-label">File Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="file_name" name="file_name" required
                       value="<?= old('file_name', $file['file_name']) ?>">
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3"><?= old('description', $file['description']) ?></textarea>
            </div>

            <div class="mb-3">
                <label class="form-label">Current File</label>
                <div class="d-flex align-items-center border rounded p-2">
                    <?php
                    $icon = 'fas fa-file';
                    if (strpos($file['file_type'], 'image') !== false) {
                        $icon = 'fas fa-file-image text-info';
                    } elseif (strpos($file['file_type'], 'pdf') !== false) {
                        $icon = 'fas fa-file-pdf text-danger';
                    } elseif (strpos($file['file_type'], 'word') !== false || strpos($file['file_type'], 'document') !== false) {
                        $icon = 'fas fa-file-word text-primary';
                    } elseif (strpos($file['file_type'], 'excel') !== false || strpos($file['file_type'], 'spreadsheet') !== false) {
                        $icon = 'fas fa-file-excel text-success';
                    } elseif (strpos($file['file_type'], 'powerpoint') !== false || strpos($file['file_type'], 'presentation') !== false) {
                        $icon = 'fas fa-file-powerpoint text-warning';
                    } elseif (strpos($file['file_type'], 'zip') !== false || strpos($file['file_type'], 'archive') !== false) {
                        $icon = 'fas fa-file-archive text-secondary';
                    } elseif (strpos($file['file_type'], 'text') !== false) {
                        $icon = 'fas fa-file-alt text-info';
                    }
                    ?>
                    <i class="<?= $icon ?> fa-2x me-3"></i>
                    <div>
                        <div><?= esc($file['file_name']) ?></div>
                        <small class="text-muted"><?= esc($file['file_type']) ?>, <?= formatFileSize($file['file_size']) ?></small>
                    </div>
                    <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="btn btn-sm btn-outline-primary ms-auto">
                        <i class="fas fa-eye me-1"></i> View
                    </a>
                </div>
            </div>

            <div class="mb-3">
                <label for="document_file" class="form-label">Replace File</label>
                <input type="file" class="form-control" id="document_file" name="document_file">
                <div class="form-text">Optional. Upload a new file to replace the current one (maximum size: 500MB). Leave empty to keep the current file.</div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="<?= base_url('staff/office/documents/files/' . $folder['id']) ?>" class="btn btn-outline-secondary me-md-2">Cancel</a>
                <button type="submit" class="btn btn-primary">Update File</button>
            </div>
        </form>
    </div>
</div>

<?php
// Helper function to format file size
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<?= $this->endSection() ?>
