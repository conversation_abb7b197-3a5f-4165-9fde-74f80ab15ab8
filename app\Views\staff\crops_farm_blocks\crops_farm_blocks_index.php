<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        <h2 class="mb-1">Crops Farm Blocks Management</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?= base_url('staff/') ?>" class="text-primary">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Crops Farm Blocks</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="<?= base_url('staff/') ?>" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
        <a href="<?= base_url('staff/crops-farm-blocks/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Crops Farm Block
        </a>
    </div>
</div>

<!-- Content Section -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Crops Farm Blocks List</h5>
    </div>
    <div class="card-body">
        <?php if (session()->getFlashdata('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= session()->getFlashdata('success') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= session()->getFlashdata('error') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($farm_blocks)): ?>
            <!-- Table Controls -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex align-items-center">
                    <label for="entriesSelect" class="form-label me-2 mb-0">Show</label>
                    <select class="form-select form-select-sm me-2" id="entriesSelect" style="width: auto;">
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="text-muted">crops farm blocks per page</span>
                </div>
                <div>
                    <input type="text" class="form-control form-control-sm" id="searchInput" placeholder="Search crops farm blocks..." style="width: 250px;">
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover" id="farmBlocksTable">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Block Code</th>
                            <th>Farmer</th>
                            <th>Crop</th>
                            <th>Location</th>
                            <th>Village</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($farm_blocks as $index => $block): ?>
                        <tr>
                            <td><?= $index + 1 ?></td>
                            <td><strong><?= esc($block['block_code'] ?? '') ?></strong></td>
                            <td><?= esc($block['farmer_name'] ?? 'N/A') ?></td>
                            <td>
                                <?php if (!empty($block['crop_name'])): ?>
                                    <span class="badge" style="background-color: <?= esc($block['crop_color_code'] ?? '#6c757d') ?>">
                                        <?= esc($block['crop_name']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td><?= esc(($block['llg_name'] ?? 'N/A') . ' - ' . ($block['ward_name'] ?? 'N/A')) ?></td>
                            <td><?= esc($block['village'] ?? '') ?></td>
                            <td>
                                <?php if (($block['status'] ?? '') == 'active'): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php elseif (($block['status'] ?? '') == 'inactive'): ?>
                                    <span class="badge bg-warning">Inactive</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary"><?= ucfirst(esc($block['status'] ?? 'unknown')) ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?= base_url('staff/crops-farm-blocks/' . $block['id']) ?>"
                                       class="btn btn-sm btn-info" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= base_url('staff/crops-farm-blocks/' . $block['id'] . '/edit') ?>"
                                       class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <!-- Empty State - No DataTable -->
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-tractor fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No Crops Farm Blocks Found</h5>
                    <p class="text-muted">You haven't created any crops farm blocks yet.</p>
                </div>
                <a href="<?= base_url('staff/crops-farm-blocks/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Your First Crops Farm Block
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>



<script>

// Initialize DataTable only if table exists and has data
$(document).ready(function() {
    const table = $('#farmBlocksTable');
    const entriesSelect = $('#entriesSelect');
    const searchInput = $('#searchInput');

    // Only initialize DataTable if the table exists and has data rows
    if (table.length && table.find('tbody tr').length > 0) {
        try {
            const dataTable = table.DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [7] }
                ],
                language: {
                    emptyTable: "No crops farm blocks found",
                    zeroRecords: "No matching crops farm blocks found"
                },
                dom: 'rt<"d-flex justify-content-between align-items-center mt-3"<"text-muted"i><"pagination-wrapper"p>>',
                drawCallback: function(settings) {
                    // Update info text to match the reference page style
                    const info = this.api().page.info();
                    const infoText = `Showing ${info.start + 1} to ${info.end} of ${info.recordsTotal} crops farm blocks`;
                    $('.dataTables_info').text(infoText);
                }
            });

            // Handle entries per page change
            entriesSelect.on('change', function() {
                dataTable.page.len(parseInt(this.value)).draw();
            });

            // Handle search
            searchInput.on('keyup', function() {
                dataTable.search(this.value).draw();
            });

        } catch (error) {
            console.error('DataTable initialization error:', error);
            // Fallback: show error message
            table.after('<div class="alert alert-warning mt-3"><i class="fas fa-exclamation-triangle me-2"></i>Table features are temporarily unavailable. Data is still visible above.</div>');
        }
    } else {
        // Hide controls if no data
        entriesSelect.closest('.d-flex').hide();
    }
});
</script>

<?= $this->endSection() ?>
