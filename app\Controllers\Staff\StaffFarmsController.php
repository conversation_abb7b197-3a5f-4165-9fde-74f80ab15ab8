<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\{
    CropsFarmBlockModel,
    FarmerInformationModel,
    CropsModel,
    AdxDistrictModel,
    AdxLlgModel,
    AdxWardModel
};

class StaffFarmsController extends BaseController
{
    protected $farmBlockModel;
    protected $farmerModel;
    protected $cropsModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;

    public function __construct()
    {
        $this->farmBlockModel = new CropsFarmBlockModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->cropsModel = new CropsModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
        $this->wardModel = new AdxWardModel();
        
        // Initialize helpers
        helper(['form', 'url', 'info']);
    }

    /**
     * Display a listing of farm blocks
     */
    public function index()
    {
        $district_id = session()->get('district_id');
        
        $data = [
            'title' => 'Crops Farm Blocks',
            'page_header' => 'Crops Farm Blocks Management',
            'farm_blocks' => $this->farmBlockModel->getFarmBlocksWithDetails($district_id),
            'farmers' => $this->farmerModel->where('district_id', $district_id)
                                          ->where('status', 'active')
                                          ->findAll(),
            'crops' => $this->cropsModel->findAll()
        ];

        return view('staff/staff_farms/staff_farms_index', $data);
    }

    /**
     * Show the form for creating a new farm block
     */
    public function create()
    {
        $district_id = session()->get('district_id');
        
        $data = [
            'title' => 'Create Farm Block',
            'page_header' => 'Create New Crops Farm Block',
            'farmers' => $this->farmerModel->where('district_id', $district_id)
                                          ->where('status', 'active')
                                          ->findAll(),
            'crops' => $this->cropsModel->findAll(),
            'llgs' => $this->llgModel->where('district_id', $district_id)->findAll(),
            'wards' => [] // Will be populated via AJAX
        ];

        return view('staff/staff_farms/staff_farms_create', $data);
    }

    /**
     * Store a newly created farm block
     */
    public function store()
    {
        $validation = \Config\Services::validation();
        
        $rules = [
            'farmer_id' => 'required|numeric',
            'crop_id' => 'required|numeric',
            'block_code' => 'required|max_length[50]',
            'llg_id' => 'required|numeric',
            'ward_id' => 'required|numeric',
            'village' => 'required|max_length[100]',
            'block_site' => 'required|max_length[200]',
            'lon' => 'permit_empty|max_length[50]',
            'lat' => 'permit_empty|max_length[50]',
            'remarks' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'exercise_id' => session()->get('exercise_id'),
            'farmer_id' => $this->request->getPost('farmer_id'),
            'crop_id' => $this->request->getPost('crop_id'),
            'block_code' => $this->request->getPost('block_code'),
            'org_id' => session()->get('org_id'),
            'country_id' => session()->get('country_id'),
            'province_id' => session()->get('province_id'),
            'district_id' => session()->get('district_id'),
            'llg_id' => $this->request->getPost('llg_id'),
            'ward_id' => $this->request->getPost('ward_id'),
            'village' => $this->request->getPost('village'),
            'block_site' => $this->request->getPost('block_site'),
            'lon' => $this->request->getPost('lon'),
            'lat' => $this->request->getPost('lat'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active',
            'created_by' => session()->get('emp_id')
        ];

        try {
            $this->farmBlockModel->save($data);
            return redirect()->to('/staff/farms')->with('success', 'Farm block created successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Store Farm Block] ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to create farm block. Please try again.');
        }
    }

    /**
     * Display the specified farm block
     */
    public function show($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->where('id', $id)
                                         ->where('district_id', $district_id)
                                         ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/farms')->with('error', 'Farm block not found.');
        }

        // Get related data
        $farmer = $this->farmerModel->find($farmBlock['farmer_id']);
        $crop = $this->cropsModel->find($farmBlock['crop_id']);
        $llg = $this->llgModel->find($farmBlock['llg_id']);
        $ward = $this->wardModel->find($farmBlock['ward_id']);

        $data = [
            'title' => 'Farm Block Details',
            'page_header' => 'Farm Block Details',
            'farm_block' => $farmBlock,
            'farmer' => $farmer,
            'crop' => $crop,
            'llg' => $llg,
            'ward' => $ward
        ];

        return view('staff/staff_farms/staff_farms_show', $data);
    }

    /**
     * Show the form for editing the specified farm block
     */
    public function edit($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->where('id', $id)
                                         ->where('district_id', $district_id)
                                         ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/farms')->with('error', 'Farm block not found.');
        }

        $data = [
            'title' => 'Edit Farm Block',
            'page_header' => 'Edit Farm Block',
            'farm_block' => $farmBlock,
            'farmers' => $this->farmerModel->where('district_id', $district_id)
                                          ->where('status', 'active')
                                          ->findAll(),
            'crops' => $this->cropsModel->findAll(),
            'llgs' => $this->llgModel->where('district_id', $district_id)->findAll(),
            'wards' => $this->wardModel->where('llg_id', $farmBlock['llg_id'])->findAll()
        ];

        return view('staff/staff_farms/staff_farms_edit', $data);
    }

    /**
     * Update the specified farm block
     */
    public function update($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->where('id', $id)
                                         ->where('district_id', $district_id)
                                         ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/farms')->with('error', 'Farm block not found.');
        }

        $validation = \Config\Services::validation();
        
        $rules = [
            'farmer_id' => 'required|numeric',
            'crop_id' => 'required|numeric',
            'block_code' => 'required|max_length[50]',
            'llg_id' => 'required|numeric',
            'ward_id' => 'required|numeric',
            'village' => 'required|max_length[100]',
            'block_site' => 'required|max_length[200]',
            'lon' => 'permit_empty|max_length[50]',
            'lat' => 'permit_empty|max_length[50]',
            'remarks' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'farmer_id' => $this->request->getPost('farmer_id'),
            'crop_id' => $this->request->getPost('crop_id'),
            'block_code' => $this->request->getPost('block_code'),
            'llg_id' => $this->request->getPost('llg_id'),
            'ward_id' => $this->request->getPost('ward_id'),
            'village' => $this->request->getPost('village'),
            'block_site' => $this->request->getPost('block_site'),
            'lon' => $this->request->getPost('lon'),
            'lat' => $this->request->getPost('lat'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('emp_id')
        ];

        try {
            $this->farmBlockModel->update($id, $data);
            return redirect()->to('/staff/farms')->with('success', 'Farm block updated successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Update Farm Block] ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to update farm block. Please try again.');
        }
    }

    /**
     * Remove the specified farm block (soft delete)
     */
    public function destroy($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->where('id', $id)
                                         ->where('district_id', $district_id)
                                         ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/farms')->with('error', 'Farm block not found.');
        }

        try {
            $data = [
                'status' => 'deleted',
                'deleted_by' => session()->get('emp_id'),
                'deleted_at' => date('Y-m-d H:i:s')
            ];
            
            $this->farmBlockModel->update($id, $data);
            return redirect()->to('/staff/farms')->with('success', 'Farm block deleted successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Delete Farm Block] ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to delete farm block. Please try again.');
        }
    }

    /**
     * AJAX method to get wards by LLG
     */
    public function getWards()
    {
        $llg_id = $this->request->getPost('llg_id');
        $wards = $this->wardModel->where('llg_id', $llg_id)->findAll();
        
        return $this->response->setJSON($wards);
    }
}
