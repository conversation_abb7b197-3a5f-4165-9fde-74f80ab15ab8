<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped text-nowrap" id="farmBlocksTable">
                <thead>
                    <tr>
                        <th class="text-nowrap">Block Code</th>
                        <th class="text-nowrap">Farmer</th>
                        <th class="text-nowrap">Crop</th>
                        <th class="text-nowrap">Location</th>
                        <th class="text-nowrap">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($farm_blocks as $block): ?>
                        <tr>
                            <td class="text-nowrap"><?= esc($block['block_code']) ?></td>
                            <td class="text-nowrap"><?= esc($block['given_name']) ?> <?= esc($block['surname']) ?></td>
                            <td class="text-nowrap"><?= esc($block['crop_name']) ?></td>
                            <td class="text-nowrap">
                                <?= esc($block['district_name']) ?> / 
                                <?= esc($block['llg_name']) ?> / 
                                <?= esc($block['ward_name']) ?>
                            </td>
                            <td class="text-nowrap">
                                <a href="<?= base_url('staff/farms/view-fertilizer-data/' . $block['id']) ?>" 
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> View Fertilizer Data
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#farmBlocksTable').DataTable({
        responsive: false,
        processing: true,
        order: [[0, 'desc']]
    });
});
</script>
<?= $this->endSection() ?> 