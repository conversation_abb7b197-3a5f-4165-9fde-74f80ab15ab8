<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3 d-flex justify-content-between">
        <a href="<?= base_url('staff/farms/crops-blocks') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Crops Data
        </a>
        <div>
            <!-- Button trigger modal -->
            <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addCropsDataModal">
                <i class="fas fa-plus-circle"></i> Add Crops Data
            </button>
            <!-- Modal -->
            <div class="modal fade" id="addCropsDataModal" tabindex="-1" aria-labelledby="addCropsDataModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title" id="addCropsDataModalLabel"><i class="fas fa-plus"></i> Add Crops Data</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <?= form_open_multipart('staff/farms/add-crops-data', ['id' => 'addCropsDataForm']) ?>
                        <div class="modal-body text-dark">
                            <input type="hidden" name="block_id" value="<?= $block['id'] ?>">
                            <input type="hidden" name="crop_id" value="<?= $crop['id'] ?>">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="action_type" class="form-label">Action Type *</label>
                                        <select class="form-select" name="action_type" id="action_type" required>
                                            <option value="">Select action type</option>
                                            <option value="add">Add</option>
                                            <option value="remove">Remove</option>
                                        </select>
                                        <div class="invalid-feedback" id="action_type_error"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="action_reason" class="form-label">Action Reason *</label>
                                        <input type="text" class="form-control" id="action_reason" name="action_reason" required>
                                        <div class="form-text">eg. new planting, disease, disaster, etc.</div>
                                        <div class="invalid-feedback" id="action_reason_error"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="action_date" class="form-label">Action Date *</label>
                                        <input type="date" class="form-control" id="action_date" name="action_date" required>
                                        <div class="invalid-feedback" id="action_date_error"></div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="number_of_plants" class="form-label">Number of Plants *</label>
                                        <input type="number" class="form-control" id="number_of_plants" name="number_of_plants" required>
                                        <div class="invalid-feedback" id="number_of_plants_error"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="breed" class="form-label">Breed *</label>
                                        <input type="text" class="form-control" id="breed" name="breed" required>
                                        <div class="invalid-feedback" id="breed_error"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="hectares" class="form-label">Hectares *</label>
                                        <input type="number" step="0.01" class="form-control" id="hectares" name="hectares" required>
                                        <div class="invalid-feedback" id="hectares_error"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                            </div>

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-success" id="btnAddCropsData">
                                <i class="fa fa-paper-plane"></i> Save Crops Data
                            </button>
                        </div>
                        <?= form_close() ?>

                        <script>
                            $(document).ready(function() {
                                // Add keypress event listener to the form input fields
                                $('#addCropsDataForm input').keypress(function(e) {
                                    if (e.which == 13) {
                                        e.preventDefault(); // Prevent the default form submission
                                        $('#btnAddCropsData').click(); // Trigger the AJAX function
                                    }
                                });

                                $('#btnAddCropsData').on('click', function() {
                                    // Reset previous error states
                                    $('.is-invalid').removeClass('is-invalid');
                                    $('.invalid-feedback').html('');

                                    // Validate required fields
                                    let isValid = true;
                                    const requiredFields = ['action_type', 'action_reason', 'action_date', 'number_of_plants', 'breed', 'hectares'];
                                    
                                    requiredFields.forEach(field => {
                                        const value = $(`#${field}`).val();
                                        if (!value || value.trim() === '') {
                                            $(`#${field}`).addClass('is-invalid');
                                            $(`#${field}_error`).html('This field is required');
                                            isValid = false;
                                        }
                                    });

                                    if (!isValid) {
                                        return;
                                    }

                                    // Create FormData object to store form data
                                    var formData = new FormData($('#addCropsDataForm')[0]);

                                    // Send an AJAX request
                                    $.ajax({
                                        url: "<?= base_url('staff/farms/add-crops-data'); ?>",
                                        type: 'POST',
                                        data: formData,
                                        contentType: false,
                                        processData: false,
                                        beforeSend: function() {
                                            // Display a loading indicator
                                            $('#btnAddCropsData').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Saving...');
                                        },
                                        success: function(response) {
                                            if (response.status === 'success') {
                                                toastr.success(response.message);
                                                setTimeout(function() {
                                                    location.reload();
                                                }, 1000);
                                            } else {
                                                toastr.error(response.message);
                                                $('#btnAddCropsData').prop('disabled', false).html('<i class="fa fa-paper-plane"></i> Save Crops Data');
                                            }
                                        },
                                        error: function(error) {
                                            console.log(error.responseText);
                                            toastr.error('An error occurred while saving data');
                                            $('#btnAddCropsData').prop('disabled', false).html('<i class="fa fa-paper-plane"></i> Save Crops Data');
                                        }
                                    });
                                });
                            });
                        </script>

                    </div>
                </div>
            </div>



        </div>
    </div>
</div>

<div class="row">
    <!-- Farm Block Details Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Block Code:</strong> <?= esc($block['block_code']) ?></p>
                <p><strong>Crop:</strong> <?= esc($crop['crop_name']) ?></p>
                <p><strong>Farmer:</strong> <?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></p>
                <p><strong>Remarks:</strong> <?= esc($block['remarks']) ?: 'No remarks' ?></p>
            </div>
        </div>
    </div>
    
    <!-- Farm Block Location Card -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Location</h5>
            </div>
            <div class="card-body">
                <p><strong>Village:</strong> <?= esc($block['village']) ?></p>
                <p><strong>Block Site:</strong> <?= esc($block['block_site']) ?></p>
                <p><strong>Province:</strong> <?= esc($province['name']) ?>, <?= esc($district['name']) ?>, <?= esc($llg['name']) ?>, <?= esc($ward['name']) ?></p>
                <p><strong>Coordinates:</strong> <?= esc($block['lon']) ?>, <?= esc($block['lat']) ?></p>
            </div>
        </div>
    </div>
    
</div>

<!-- Block History Section -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Block History
                    <div class="float-end">
                        <span class="badge bg-warning">Total Plants: <?= $total_plants_added['total_plants_added'] - $total_plants_removed['total_plants_removed'] ?></span>
                        <span class="badge bg-success ms-2">Total Hectares: <?= $total_hectares_added['total_hectares_added'] - $total_hectares_removed['total_hectares_removed'] ?></span>
                    </div>
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped text-nowrap" id="blockDataTable">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Action</th>
                                <th>Reason</th>
                                <th>Plants</th>
                                <th>Breed</th>
                                <th>Hectares</th>
                                <th>Remarks</th>
                                <th>Created By</th>
                                <th>Created At</th>
                                <th>Updated By</th>
                                <th>Updated At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($crops_data)): ?>
                                <?php foreach ($crops_data as $data): ?>
                                    <tr>
                                        <td><?= date('d M Y', strtotime($data['action_date'])) ?></td>
                                        <td>
                                            <?php if ($data['action_type'] === 'add'): ?>
                                                <span class="badge bg-success">Add</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Remove</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= esc($data['action_reason']) ?></td>
                                        <td class="text-end"><?= number_format($data['number_of_plants']) ?></td>
                                        <td><?= esc($data['breed']) ?></td>
                                        <td class="text-end"><?= number_format($data['hectares'], 2) ?></td>
                                        <td><?= esc($data['remarks']) ?: '-' ?></td>
                                        <td><?php
                                            foreach ($users as $user) {
                                                if ($user['id'] === $data['created_by']) {
                                                    echo esc($user['name']);
                                                    break;
                                                }
                                            }
                                        ?></td>
                                        <td><?= date('d M Y H:i', strtotime($data['created_at'])) ?></td>
                                        <td><?php
                                            foreach ($users as $user) {
                                                if ($user['id'] === $data['updated_by']) {
                                                    echo esc($user['name']);
                                                    break;
                                                }
                                            }
                                        ?></td>
                                        <td><?= date('d M Y H:i', strtotime($data['updated_at'])) ?></td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-primary" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#editCropsDataModal"
                                                    data-id="<?= $data['id'] ?>"
                                                    data-block_id="<?= $data['block_id'] ?>"
                                                    data-crop_id="<?= $data['crop_id'] ?>"
                                                    data-action_type="<?= $data['action_type'] ?>"
                                                    data-action_reason="<?= $data['action_reason'] ?>"
                                                    data-action_date="<?= $data['action_date'] ?>"
                                                    data-number_of_plants="<?= $data['number_of_plants'] ?>"
                                                    data-breed="<?= $data['breed'] ?>"
                                                    data-hectares="<?= $data['hectares'] ?>"
                                                    data-remarks="<?= $data['remarks'] ?>">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editCropsDataModal" tabindex="-1" aria-labelledby="editCropsDataModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editCropsDataModalLabel"><i class="fas fa-edit"></i> Edit Crops Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open_multipart('staff/farms/update-crops-data', ['id' => 'editCropsDataForm']) ?>
            <div class="modal-body text-dark">
                <input type="hidden" name="id" id="edit_id">
                <input type="hidden" name="block_id" id="edit_block_id">
                <input type="hidden" name="crop_id" id="edit_crop_id">

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_action_type" class="form-label">Action Type *</label>
                            <select class="form-select" name="action_type" id="edit_action_type" required>
                                <option value="">Select action type</option>
                                <option value="add">Add</option>
                                <option value="remove">Remove</option>
                            </select>
                            <div class="invalid-feedback" id="edit_action_type_error"></div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_action_reason" class="form-label">Action Reason *</label>
                            <input type="text" class="form-control" id="edit_action_reason" name="action_reason" required>
                            <div class="form-text">eg. new planting, disease, disaster, etc.</div>
                            <div class="invalid-feedback" id="edit_action_reason_error"></div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_action_date" class="form-label">Action Date *</label>
                            <input type="date" class="form-control" id="edit_action_date" name="action_date" required>
                            <div class="invalid-feedback" id="edit_action_date_error"></div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_number_of_plants" class="form-label">Number of Plants *</label>
                            <input type="number" class="form-control" id="edit_number_of_plants" name="number_of_plants" required>
                            <div class="invalid-feedback" id="edit_number_of_plants_error"></div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_breed" class="form-label">Breed *</label>
                            <input type="text" class="form-control" id="edit_breed" name="breed" required>
                            <div class="invalid-feedback" id="edit_breed_error"></div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_hectares" class="form-label">Hectares *</label>
                            <input type="number" step="0.01" class="form-control" id="edit_hectares" name="hectares" required>
                            <div class="invalid-feedback" id="edit_hectares_error"></div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="edit_remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="btnUpdateCropsData">
                    <i class="fa fa-save"></i> Update Block Data
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Destroy existing DataTable if it exists
    if ($.fn.DataTable.isDataTable('#cropsDataTable')) {
        $('#cropsDataTable').DataTable().destroy();
    }
    
    // Initialize DataTable
    $('#cropsDataTable').DataTable({
        responsive: false,
        processing: true,
        columnDefs: [
            { orderable: true, targets: '_all' }
        ],
        order: [[0, 'desc']]
    });
});

// Add update functionality
$('#btnUpdateCropsData').on('click', function() {
    // Reset previous error states
    $('.is-invalid').removeClass('is-invalid');
    $('.invalid-feedback').html('');

    // Validate required fields
    let isValid = true;
    const requiredFields = ['action_type', 'action_reason', 'action_date', 'number_of_plants', 'breed', 'hectares'];
    
    requiredFields.forEach(field => {
        const value = $(`#edit_${field}`).val();
        if (!value || value.trim() === '') {
            $(`#edit_${field}`).addClass('is-invalid');
            $(`#edit_${field}_error`).html('This field is required');
            isValid = false;
        }
    });

    if (!isValid) {
        return;
    }

    // Create FormData object
    var formData = new FormData($('#editCropsDataForm')[0]);

    // Send AJAX request
    $.ajax({
        url: "<?= base_url('staff/farms/update-crops-data'); ?>",
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        beforeSend: function() {
            $('#btnUpdateCropsData').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Updating...');
        },
        success: function(response) {
            if (response.status === 'success') {
                toastr.success(response.message);
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                toastr.error(response.message);
                $('#btnUpdateCropsData').prop('disabled', false).html('<i class="fa fa-save"></i> Update Crops Data');
            }
        },
        error: function(error) {
            console.log(error.responseText);
            toastr.error('An error occurred while updating data');
            $('#btnUpdateCropsData').prop('disabled', false).html('<i class="fa fa-save"></i> Update Crops Data');
        }
    });
});

// Handle edit modal data population
$('#editCropsDataModal').on('show.bs.modal', function (event) {
    var button = $(event.relatedTarget); // Button that triggered the modal
    
    // Get data attributes from the button
    var id = button.data('id');
    var block_id = button.data('block_id');
    var crop_id = button.data('crop_id');
    var action_type = button.data('action_type');
    var action_reason = button.data('action_reason');
    var action_date = button.data('action_date');
    var number_of_plants = button.data('number_of_plants');
    var breed = button.data('breed');
    var hectares = button.data('hectares');
    var remarks = button.data('remarks');
    
    // Populate the form fields
    var modal = $(this);
    modal.find('#edit_id').val(id);
    modal.find('#edit_block_id').val(block_id);
    modal.find('#edit_crop_id').val(crop_id);
    modal.find('#edit_action_type').val(action_type);
    modal.find('#edit_action_reason').val(action_reason);
    modal.find('#edit_action_date').val(action_date);
    modal.find('#edit_number_of_plants').val(number_of_plants);
    modal.find('#edit_breed').val(breed);
    modal.find('#edit_hectares').val(hectares);
    modal.find('#edit_remarks').val(remarks);
});

// Alternative method using AJAX if you prefer
function editCropsData(id) {
    // Reset previous error states
    $('.is-invalid').removeClass('is-invalid');
    $('.invalid-feedback').html('');
    
    // Fetch block data details
    $.ajax({
        url: "<?= base_url('staff/farms/get-crops-data/'); ?>" + id,
        type: 'GET',
        success: function(response) {
            if (response.status === 'success') {
                const data = response.data;
                
                // Populate the form fields
                $('#edit_id').val(data.id);
                $('#edit_block_id').val(data.block_id);
                $('#edit_crop_id').val(data.crop_id);
                $('#edit_action_type').val(data.action_type);
                $('#edit_action_reason').val(data.action_reason);
                $('#edit_action_date').val(data.action_date);
                $('#edit_number_of_plants').val(data.number_of_plants);
                $('#edit_breed').val(data.breed);
                $('#edit_hectares').val(data.hectares);
                $('#edit_remarks').val(data.remarks);
                
                // Show the modal
                $('#editCropsDataModal').modal('show');
            } else {
                toastr.error('Failed to fetch crops data details');
            }
        },
        error: function() {
            toastr.error('An error occurred while fetching crops data details');
        }
    });
}
</script>
<?= $this->endSection() ?>