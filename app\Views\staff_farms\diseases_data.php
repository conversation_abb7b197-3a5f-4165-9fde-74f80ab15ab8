<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3">
        <div class="card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center flex-wrap gap-2">
                <div>
                    <i class="fas fa-bug me-1"></i>
                    Farm Blocks Disease Data
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="datatablesSimple" class="table table-bordered table-striped" style="width:100%">
                        <thead>
                            <tr>
                                <th class="text-nowrap">Block Code</th>
                                <th class="text-nowrap">Farmer</th>
                                <th class="text-nowrap">Crop</th>
                                <th class="text-nowrap">Location</th>
                                <th class="text-center text-nowrap">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($farm_blocks as $block): ?>
                            <tr>
                                <td class="text-nowrap" data-label="Block Code"><?= esc($block['block_code']) ?></td>
                                <td class="text-nowrap" data-label="Farmer"><?= esc($block['given_name']) . ' ' . esc($block['surname']) ?></td>
                                <td class="text-nowrap" data-label="Crop"><?= esc($block['crop_name']) ?></td>
                                <td class="text-nowrap" data-label="Location">
                                    <div class="d-flex flex-column">
                                        <span><?= esc($block['village']) ?></span>
                                        <small class="text-muted">
                                            <?= esc($block['ward_name']) ?>, 
                                            <?= esc($block['llg_name']) ?>, 
                                            <?= esc($block['district_name']) ?>
                                        </small>
                                    </div>
                                </td>
                                <td class="text-center text-nowrap">
                                    <a href="<?= base_url('staff/farms/view-diseases-data/' . $block['id']) ?>" 
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i> <span class="d-none d-md-inline">View Disease Data</span>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#datatablesSimple').DataTable({
        responsive: false,
        scrollX: true,
        pageLength: 25,
        order: [[0, 'asc']],
        columnDefs: [
            { orderable: true, targets: '_all' },
            { className: 'text-center', targets: -1 }
        ],
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        language: {
            search: "_INPUT_",
            searchPlaceholder: "Search records..."
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>